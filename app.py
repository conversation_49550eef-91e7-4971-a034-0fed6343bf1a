# -*- coding: utf-8 -*-
from reprb import reprb
from flask import Flask, render_template, request, jsonify, redirect, url_for
import os
import tempfile
import json
from consts import ALL_RISK, ALL_DBFLAGS, ALL_beginchar

app = Flask(__name__, template_folder="templates")


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/upload_file", methods=["POST"])
def upload_file():
    file = request.files["file"]
    purpose = request.form.get("purpose")

    if not purpose:
        return jsonify({"message": "未指定标记目的"}), 400

    if file:
        with tempfile.NamedTemporaryFile(
            delete=False, mode="wb", suffix=".reprb"
        ) as tmpfile:
            file.save(tmpfile)
            tmpfile_path = tmpfile.name

        return jsonify({"tmpfile_path": tmpfile_path})
    else:
        return jsonify({"message": "No file uploaded"}), 400


@app.route("/upload_files", methods=["POST"])
def upload_files():
    files = request.files.getlist("files")
    purpose = request.form.get("purpose")

    if not purpose:
        return jsonify({"message": "未指定标记目的"}), 400

    if not files:
        return jsonify({"message": "No files uploaded"}), 400

    # 创建一个临时文件来合并所有上传的文件
    with tempfile.NamedTemporaryFile(
        delete=False, mode="wb", suffix=".reprb"
    ) as tmpfile:
        for file in files:
            # 读取每个文件的内容并写入临时文件
            file_content = file.read()
            tmpfile.write(file_content)
            # 如果文件内容不以换行符结尾，添加一个换行符
            if file_content and not file_content.endswith(b'\n'):
                tmpfile.write(b'\n')

        tmpfile_path = tmpfile.name

    return jsonify({"tmpfile_path": tmpfile_path})


@app.route("/process_text", methods=["POST"])
def process_text():
    text_content = request.form.get("text")
    text_format = request.form.get("format", "utf8")  # 默认为 utf8 格式

    if text_content:
        try:
            content_bytes = text_content.encode("utf-8")
            # 如果是 UTF-8 格式，需要转换为 REPRB 格式
            if text_format == "utf8":
                content_bytes = reprb(content_bytes)

            # 使用二进制模式写入
            with tempfile.NamedTemporaryFile(
                delete=False, mode="wb", suffix=".reprb"
            ) as tmpfile:
                tmpfile.write(content_bytes)
                tmpfile_path = tmpfile.name

            return jsonify({"tmpfile_path": tmpfile_path})
        except Exception as e:
            return jsonify({"message": f"处理文本失败: {str(e)}"}), 500
    else:
        return jsonify({"message": "No text provided"}), 400


@app.route("/label_page")
def label_page():
    tmpfile_path = request.args.get("tmpfile_path")
    purpose = request.args.get("purpose")

    if not tmpfile_path or not os.path.exists(tmpfile_path):
        return "临时文件路径丢失或文件不存在！", 400

    if not purpose:
        return "未指定标记目的！", 400

    from reprb import evalb

    processed_lines = []
    total_chars = 0
    # 从临时的reprb文件中读取reprb_bytes形式的payload
    with open(tmpfile_path, "rb") as f:
        for i, reprb_payload in enumerate(f):
            try:
                display_content = evalb(reprb_payload).decode("utf-8")
            except UnicodeDecodeError:
                # 解码失败，使用repr展示bytes
                display_content = reprb_payload.decode()

            total_chars += len(display_content)
            processed_lines.append(
                {
                    "line_number": i + 1,
                    "display_content": display_content,
                    "payload": reprb_payload.decode(),
                }
            )

    risk_tags = list(ALL_RISK.keys())
    db_tags = list(ALL_DBFLAGS.keys())
    beginchar_tags = list(ALL_beginchar.keys())

    return render_template(
        "label.html",
        processed_lines=processed_lines,
        tmpfile_path=tmpfile_path,
        purpose=purpose,
        total_chars=total_chars,
        total_lines=len(processed_lines),
        risk_tags=risk_tags,
        db_tags=db_tags,
        beginchar_tags=beginchar_tags,
    )


@app.route("/submit_labels", methods=["POST"])
def submit_labels():
    from flask import send_file

    purpose = request.form.get("purpose")
    export_filename = request.form.get("export_filename")
    if not purpose:
        return jsonify({"message": "未指定标记目的！"}), 400
    if not export_filename:
        return jsonify({"message": "未指定导出文件名！"}), 400

    def asm_fp(item):
        return {
            "payload": item["payload"],
            "benign": True,
        }

    def asm_mal(item):
        return {"payload": item["payload"], "benign": False, "tags": item["tags"]}

    try:
        data = json.loads(request.form.get("data"))

        if purpose == "false_positive":
            asm_func = asm_fp
        else:
            asm_func = asm_mal

        dump_data = list(map(asm_func, data))

        with tempfile.NamedTemporaryFile(
            delete=False, mode="w", suffix=".json"
        ) as tmpfile:
            json.dump(dump_data, tmpfile)
            tmpfile.flush()  # 确保写入磁盘
            tmpfile_path = tmpfile.name

            response = send_file(
                tmpfile_path,
                as_attachment=True,
                download_name=export_filename,
                mimetype="application/json",
            )

            # @response.call_on_close
            # def cleanup():
            #     os.remove(tmpfile.name)

            return response

    except Exception as e:
        return jsonify({"message": f"保存标记时发生错误：{str(e)}"}), 500


from flask import session

# 步骤选择路由
@app.route('/steps', methods=['GET', 'POST'])
def steps():
    if request.method == 'POST':
        # 获取用户选择的步骤
        step1 = request.form.get('step1')
        step2 = request.form.get('step2')

        # 根据选择跳转到不同界面
        if step1 == 'option1' and step2 == 'optionA':
            return redirect(url_for('label_page'))
        elif step1 == 'option1' and step2 == 'optionB':
            return redirect(url_for('tags'))
        elif step1 == 'option2' and step2 == 'optionA':
            return redirect(url_for('index'))
        else:
            return redirect(url_for('steps'))

    # 首次访问显示选择表单
    return render_template('steps.html')


# 训练任务创建路由
@app.route('/training_task')
def training_task():
    """显示训练任务创建页面"""
    return render_template('training_task.html')


@app.route('/create_training_task', methods=['POST'])
def create_training_task():
    """处理训练任务创建请求"""
    try:
        data = request.get_json()

        # 验证数据结构
        if not data or 'repoA' not in data or 'repoB' not in data or 'repoC' not in data:
            return jsonify({'success': False, 'message': '数据格式错误'}), 400

        repo_a = data['repoA']
        repo_b = data['repoB']
        repo_c = data['repoC']

        # 验证每个仓库的数据
        for repo_name, repo_data in [('A', repo_a), ('B', repo_b), ('C', repo_c)]:
            if not repo_data.get('type') or not repo_data.get('value'):
                return jsonify({
                    'success': False,
                    'message': f'仓库{repo_name}的配置不完整'
                }), 400

            if repo_data['type'] not in ['branch', 'tag']:
                return jsonify({
                    'success': False,
                    'message': f'仓库{repo_name}的类型无效'
                }), 400

        # 验证依赖关系
        validation_result = validate_dependencies(repo_a, repo_b, repo_c)
        if not validation_result['valid']:
            return jsonify({
                'success': False,
                'message': validation_result['message']
            }), 400

        # 构建训练任务配置
        task_config = {
            'repositories': {
                'A': {
                    'type': repo_a['type'],
                    'value': repo_a['value'],
                    'pipeline_source': repo_a['value']
                },
                'B': {
                    'type': repo_b['type'],
                    'value': repo_b['value'],
                    'pipeline_source': repo_b['value']
                },
                'C': {
                    'type': repo_c['type'],
                    'value': repo_c['value'],
                    'pipeline_source': repo_c['value']
                }
            },
            'dependency_chain': 'A <- B <- C',
            'created_at': json.dumps({"timestamp": "now"}),  # 实际应用中使用真实时间戳
            'status': 'pending'
        }

        # 这里应该调用实际的训练任务创建逻辑
        # 例如：创建流水线、配置依赖关系等
        # result = create_training_pipeline(task_config)

        # 模拟成功响应
        return jsonify({
            'success': True,
            'message': '训练任务创建成功',
            'task_id': 'task_' + str(hash(str(task_config)))[-8:],  # 模拟任务ID
            'config': task_config
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500


def validate_dependencies(repo_a, repo_b, repo_c):
    """验证仓库依赖关系"""

    # 规则1: 如果A选择分支训练，B和C都必须选择相同分支
    if repo_a['type'] == 'branch':
        if repo_b['type'] != 'branch':
            return {
                'valid': False,
                'message': 'A仓库选择分支训练时，B仓库也必须选择分支训练'
            }

        if repo_c['type'] != 'branch':
            return {
                'valid': False,
                'message': 'A仓库选择分支训练时，C仓库也必须选择分支训练'
            }

        # 检查分支名是否一致（在实际应用中，这里应该检查分支是否存在）
        if repo_a['value'] != repo_b['value']:
            return {
                'valid': False,
                'message': f'A仓库和B仓库必须使用相同的分支名'
            }

        if repo_b['value'] != repo_c['value']:
            return {
                'valid': False,
                'message': f'B仓库和C仓库必须使用相同的分支名'
            }

    # 规则2: 如果A选择标签，B可以选择标签或分支，C必须跟随B的选择
    elif repo_a['type'] == 'tag':
        if repo_b['type'] == 'branch':
            # B选择分支，C也必须选择分支
            if repo_c['type'] != 'branch':
                return {
                    'valid': False,
                    'message': 'B仓库选择分支训练时，C仓库也必须选择分支训练'
                }

            # 检查B和C的分支名是否一致
            if repo_b['value'] != repo_c['value']:
                return {
                    'valid': False,
                    'message': 'B仓库和C仓库必须使用相同的分支名'
                }

        elif repo_b['type'] == 'tag':
            # B选择标签，C也必须选择标签
            if repo_c['type'] != 'tag':
                return {
                    'valid': False,
                    'message': 'B仓库选择标签时，C仓库也必须选择标签'
                }

            # 检查B和C的标签名是否一致
            if repo_b['value'] != repo_c['value']:
                return {
                    'valid': False,
                    'message': 'B仓库和C仓库必须使用相同的标签名'
                }

    return {'valid': True, 'message': '依赖关系验证通过'}


if __name__ == "__main__":
    app.run(debug=True, port=5000)
