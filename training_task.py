# -*- coding: utf-8 -*-
"""
训练任务相关的数据结构和逻辑
"""
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum


class SourceType(Enum):
    """数据源类型"""
    BRANCH = "branch"
    TAG = "tag"


@dataclass
class Repository:
    """仓库信息"""
    name: str
    display_name: str
    branches: List[str]
    tags: List[str]


@dataclass
class RepositorySelection:
    """仓库选择信息"""
    repository: str
    source_type: SourceType
    source_name: str  # 分支名或标签名


@dataclass
class TrainingTask:
    """训练任务"""
    name: str
    description: str
    repo_a: RepositorySelection
    repo_b: RepositorySelection
    repo_c: RepositorySelection
    created_at: Optional[str] = None


class TrainingTaskManager:
    """训练任务管理器"""
    
    def __init__(self):
        # 模拟的仓库数据，实际应用中应该从Git API或数据库获取
        self.repositories = {
            'repo_a': Repository(
                name='repo_a',
                display_name='仓库A',
                branches=['main', 'develop', 'feature/new-model', 'hotfix/bug-fix'],
                tags=['v1.0.0', 'v1.1.0', 'v1.2.0', 'v2.0.0-beta']
            ),
            'repo_b': Repository(
                name='repo_b',
                display_name='仓库B',
                branches=['main', 'develop', 'feature/enhancement', 'release/v1.3'],
                tags=['v1.0.0', 'v1.1.0', 'v1.2.0', 'v1.3.0-rc1']
            ),
            'repo_c': Repository(
                name='repo_c',
                display_name='仓库C',
                branches=['main', 'develop', 'feature/optimization', 'bugfix/critical'],
                tags=['v1.0.0', 'v1.1.0', 'v1.2.0', 'v1.2.1']
            )
        }
    
    def get_repositories(self) -> Dict[str, Repository]:
        """获取所有仓库信息"""
        return self.repositories
    
    def validate_dependency_chain(self, repo_a: RepositorySelection, 
                                 repo_b: RepositorySelection, 
                                 repo_c: RepositorySelection) -> Dict[str, Union[bool, str]]:
        """
        验证依赖关系链 A<-B<-C
        
        规则：
        1. 如果A使用分支训练，B和C必须使用相同的分支
        2. 如果A使用已有标签，B可以选择标签或分支，C必须跟随B的选择
        """
        result = {"valid": True, "message": ""}
        
        # 规则1：如果A使用分支，B和C必须使用相同分支
        if repo_a.source_type == SourceType.BRANCH:
            if repo_b.source_type != SourceType.BRANCH:
                result["valid"] = False
                result["message"] = "仓库A使用分支训练时，仓库B必须也使用分支"
                return result
            
            if repo_c.source_type != SourceType.BRANCH:
                result["valid"] = False
                result["message"] = "仓库A使用分支训练时，仓库C必须也使用分支"
                return result
            
            # 检查分支名是否一致
            if repo_a.source_name != repo_b.source_name:
                result["valid"] = False
                result["message"] = f"仓库A使用分支'{repo_a.source_name}'时，仓库B必须使用相同分支"
                return result
            
            if repo_a.source_name != repo_c.source_name:
                result["valid"] = False
                result["message"] = f"仓库A使用分支'{repo_a.source_name}'时，仓库C必须使用相同分支"
                return result
        
        # 规则2：如果A使用标签，B可以选择标签或分支，C跟随B
        elif repo_a.source_type == SourceType.TAG:
            # B可以选择标签或分支，但C必须跟随B的选择
            if repo_b.source_type == SourceType.BRANCH:
                if repo_c.source_type != SourceType.BRANCH:
                    result["valid"] = False
                    result["message"] = "仓库B使用分支时，仓库C必须也使用分支"
                    return result
                
                if repo_b.source_name != repo_c.source_name:
                    result["valid"] = False
                    result["message"] = f"仓库B使用分支'{repo_b.source_name}'时，仓库C必须使用相同分支"
                    return result
            
            elif repo_b.source_type == SourceType.TAG:
                if repo_c.source_type != SourceType.TAG:
                    result["valid"] = False
                    result["message"] = "仓库B使用标签时，仓库C必须也使用标签"
                    return result
        
        return result
    
    def get_available_options(self, repo_name: str, repo_a_selection: Optional[RepositorySelection] = None, 
                             repo_b_selection: Optional[RepositorySelection] = None) -> Dict[str, List[str]]:
        """
        根据依赖关系获取可用的选项
        """
        if repo_name not in self.repositories:
            return {"branches": [], "tags": []}
        
        repo = self.repositories[repo_name]
        
        # 仓库A可以自由选择
        if repo_name == 'repo_a':
            return {
                "branches": repo.branches,
                "tags": repo.tags
            }
        
        # 仓库B的选择受A的限制
        elif repo_name == 'repo_b':
            if not repo_a_selection:
                return {"branches": repo.branches, "tags": repo.tags}
            
            if repo_a_selection.source_type == SourceType.BRANCH:
                # A使用分支，B必须使用相同分支
                if repo_a_selection.source_name in repo.branches:
                    return {"branches": [repo_a_selection.source_name], "tags": []}
                else:
                    return {"branches": [], "tags": []}
            else:
                # A使用标签，B可以自由选择
                return {"branches": repo.branches, "tags": repo.tags}
        
        # 仓库C的选择受A和B的限制
        elif repo_name == 'repo_c':
            if not repo_a_selection or not repo_b_selection:
                return {"branches": repo.branches, "tags": repo.tags}
            
            if repo_a_selection.source_type == SourceType.BRANCH:
                # A使用分支，C必须使用相同分支
                if repo_a_selection.source_name in repo.branches:
                    return {"branches": [repo_a_selection.source_name], "tags": []}
                else:
                    return {"branches": [], "tags": []}
            else:
                # A使用标签，C跟随B的选择
                if repo_b_selection.source_type == SourceType.BRANCH:
                    if repo_b_selection.source_name in repo.branches:
                        return {"branches": [repo_b_selection.source_name], "tags": []}
                    else:
                        return {"branches": [], "tags": []}
                else:
                    # B使用标签，C可以选择标签
                    return {"branches": [], "tags": repo.tags}
        
        return {"branches": [], "tags": []}
    
    def create_training_task(self, task_data: Dict) -> Dict[str, Union[bool, str, TrainingTask]]:
        """创建训练任务"""
        try:
            # 构建仓库选择对象
            repo_a = RepositorySelection(
                repository='repo_a',
                source_type=SourceType(task_data['repo_a']['source_type']),
                source_name=task_data['repo_a']['source_name']
            )
            
            repo_b = RepositorySelection(
                repository='repo_b',
                source_type=SourceType(task_data['repo_b']['source_type']),
                source_name=task_data['repo_b']['source_name']
            )
            
            repo_c = RepositorySelection(
                repository='repo_c',
                source_type=SourceType(task_data['repo_c']['source_type']),
                source_name=task_data['repo_c']['source_name']
            )
            
            # 验证依赖关系
            validation = self.validate_dependency_chain(repo_a, repo_b, repo_c)
            if not validation["valid"]:
                return {
                    "success": False,
                    "message": validation["message"]
                }
            
            # 创建训练任务
            task = TrainingTask(
                name=task_data['name'],
                description=task_data['description'],
                repo_a=repo_a,
                repo_b=repo_b,
                repo_c=repo_c
            )
            
            return {
                "success": True,
                "message": "训练任务创建成功",
                "task": task
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"创建训练任务失败: {str(e)}"
            }
