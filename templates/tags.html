{% extends "base.html" %}

{% block nav_tags_active %}active{% endblock %}

{% block extra_css %}
        table {
            width: 80%;
            max-width: 800px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-collapse: collapse;
            table-layout: fixed;
        }

        th,
        td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            word-break: break-word;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }

        tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }
{% endblock %}

{% block content %}
    <table>
        <thead>
            <tr>
                <th style="width: 60%;">Text</th>
                <th>Risk</th>
                <th>DB</th>
            </tr>
        </thead>
        <tbody>
            {% for tag in tags %}
            <tr>
                <td>{{ tag.text }}</td>
                <td>{{ tag.risk }}</td>
                <td>{{ tag.db }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <a href="/" class="back-link">返回首页</a>
{% endblock %}