{% extends "base.html" %}

{% block title %}创建训练任务{% endblock %}

{% block nav_training_active %}active{% endblock %}

{% block extra_css %}
    .training-container {
        max-width: 1200px;
        background: var(--card-background);
        border-radius: 16px;
        box-shadow: 0 4px 16px var(--shadow-light);
        padding: 32px;
        margin: 20px auto;
    }

    .training-title {
        text-align: center;
        color: var(--primary-color);
        margin-bottom: 32px;
        font-size: 2rem;
        font-weight: 700;
    }

    .repositories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
    }

    .repository-card {
        background: linear-gradient(145deg, #ffffff, #f8fafc);
        border: 2px solid var(--border-color);
        border-radius: 16px;
        padding: 24px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .repository-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--primary-dark-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .repository-card.active::before {
        opacity: 1;
    }

    .repository-card.active {
        border-color: var(--primary-color);
        box-shadow: 0 8px 24px rgba(26, 115, 232, 0.15);
        transform: translateY(-2px);
    }

    .repository-card.disabled {
        opacity: 0.6;
        pointer-events: none;
        background: #f5f5f5;
    }

    .repo-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        gap: 12px;
    }

    .repo-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        font-size: 1.2rem;
    }

    .repo-a .repo-icon { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
    .repo-b .repo-icon { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
    .repo-c .repo-icon { background: linear-gradient(135deg, #45b7d1, #3498db); }

    .repo-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-color);
        margin: 0;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        color: var(--text-color);
        font-weight: 500;
        font-size: 0.9rem;
    }

    .select-wrapper {
        position: relative;
        margin-bottom: 16px;
    }

    select {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: white;
        color: var(--text-color);
        font-size: 1rem;
        transition: all 0.3s ease;
        appearance: none;
        cursor: pointer;
    }

    select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
    }

    select:disabled {
        background: #f5f5f5;
        color: #999;
        cursor: not-allowed;
    }

    .select-wrapper::after {
        content: '▼';
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
        pointer-events: none;
        font-size: 0.8rem;
    }

    .radio-group {
        display: flex;
        background: var(--background-color);
        border-radius: 12px;
        padding: 4px;
        margin-bottom: 16px;
        border: 1px solid var(--border-color);
    }

    .radio-group input[type="radio"] {
        display: none;
    }

    .radio-group label {
        flex: 1;
        text-align: center;
        padding: 10px 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        color: var(--text-secondary);
        margin: 0;
    }

    .radio-group input[type="radio"]:checked + label {
        background: var(--primary-color);
        color: white;
        box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    }

    .dependency-info {
        background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 12px;
        margin-top: 16px;
        font-size: 0.85rem;
        color: #1565c0;
        line-height: 1.4;
    }

    .dependency-info.warning {
        background: linear-gradient(135deg, #fff3e0, #fce4ec);
        border-color: #ffcc02;
        color: #e65100;
    }

    .submit-section {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 24px;
        padding: 24px;
        border-top: 1px solid var(--border-color);
        margin-top: 32px;
    }

    .submit-button {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        color: white;
        padding: 16px 48px;
        border: none;
        border-radius: 12px;
        cursor: pointer;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 6px 20px rgba(26, 115, 232, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .submit-button:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 25px rgba(26, 115, 232, 0.4);
    }

    .submit-button:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    @media (max-width: 768px) {
        .repositories-grid {
            grid-template-columns: 1fr;
        }
        
        .training-container {
            padding: 20px;
            margin: 10px;
        }
        
        .submit-button {
            width: 100%;
            padding: 14px 32px;
        }
    }
{% endblock %}

{% block content %}
<div class="training-container">
    <h1 class="training-title">创建训练任务</h1>
    
    <form id="training-form">
        <div class="repositories-grid">
            <!-- A仓库 -->
            <div class="repository-card repo-a" id="repo-a-card">
                <div class="repo-header">
                    <div class="repo-icon">A</div>
                    <h3 class="repo-title">A仓库 (主仓库)</h3>
                </div>
                
                <div class="form-group">
                    <label class="form-label">选择训练方式</label>
                    <div class="radio-group">
                        <input type="radio" id="repo-a-branch" name="repo-a-type" value="branch" checked>
                        <label for="repo-a-branch">分支训练</label>
                        <input type="radio" id="repo-a-tag" name="repo-a-type" value="tag">
                        <label for="repo-a-tag">已有标签</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" id="repo-a-select-label">选择分支</label>
                    <div class="select-wrapper">
                        <select id="repo-a-select" name="repo-a-value" required>
                            <option value="">请选择...</option>
                        </select>
                    </div>
                </div>
                
                <div class="dependency-info">
                    <strong>主仓库：</strong>您的选择将影响B和C仓库的可选项
                </div>
            </div>

            <!-- B仓库 -->
            <div class="repository-card repo-b" id="repo-b-card">
                <div class="repo-header">
                    <div class="repo-icon">B</div>
                    <h3 class="repo-title">B仓库 (依赖A)</h3>
                </div>
                
                <div class="form-group">
                    <label class="form-label">选择训练方式</label>
                    <div class="radio-group">
                        <input type="radio" id="repo-b-branch" name="repo-b-type" value="branch" checked>
                        <label for="repo-b-branch">分支训练</label>
                        <input type="radio" id="repo-b-tag" name="repo-b-type" value="tag">
                        <label for="repo-b-tag">已有标签</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" id="repo-b-select-label">选择分支</label>
                    <div class="select-wrapper">
                        <select id="repo-b-select" name="repo-b-value" required>
                            <option value="">请选择...</option>
                        </select>
                    </div>
                </div>
                
                <div class="dependency-info" id="repo-b-info">
                    等待A仓库选择...
                </div>
            </div>

            <!-- C仓库 -->
            <div class="repository-card repo-c" id="repo-c-card">
                <div class="repo-header">
                    <div class="repo-icon">C</div>
                    <h3 class="repo-title">C仓库 (依赖B)</h3>
                </div>
                
                <div class="form-group">
                    <label class="form-label">选择训练方式</label>
                    <div class="radio-group">
                        <input type="radio" id="repo-c-branch" name="repo-c-type" value="branch" checked>
                        <label for="repo-c-branch">分支训练</label>
                        <input type="radio" id="repo-c-tag" name="repo-c-type" value="tag">
                        <label for="repo-c-tag">已有标签</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" id="repo-c-select-label">选择分支</label>
                    <div class="select-wrapper">
                        <select id="repo-c-select" name="repo-c-value" required>
                            <option value="">请选择...</option>
                        </select>
                    </div>
                </div>
                
                <div class="dependency-info" id="repo-c-info">
                    等待B仓库选择...
                </div>
            </div>
        </div>

        <div class="submit-section">
            <button type="submit" class="submit-button" id="submit-btn" disabled>
                创建训练任务
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 模拟数据 - 实际应用中应该从后端API获取
    const mockData = {
        branches: {
            'repo-a': ['main', 'develop', 'feature/new-model', 'hotfix/bug-123'],
            'repo-b': ['main', 'develop', 'feature/integration', 'release/v2.0'],
            'repo-c': ['main', 'develop', 'feature/pipeline', 'test/automation']
        },
        tags: {
            'repo-a': ['v1.0.0', 'v1.1.0', 'v1.2.0', 'v2.0.0-beta'],
            'repo-b': ['v1.0.0', 'v1.1.0', 'v1.2.0', 'v2.0.0-alpha'],
            'repo-c': ['v1.0.0', 'v1.1.0', 'v1.2.0', 'v2.0.0-rc1']
        }
    };

    // DOM元素
    const repoACard = document.getElementById('repo-a-card');
    const repoBCard = document.getElementById('repo-b-card');
    const repoCCard = document.getElementById('repo-c-card');
    
    const repoATypeRadios = document.querySelectorAll('input[name="repo-a-type"]');
    const repoBTypeRadios = document.querySelectorAll('input[name="repo-b-type"]');
    const repoCTypeRadios = document.querySelectorAll('input[name="repo-c-type"]');
    
    const repoASelect = document.getElementById('repo-a-select');
    const repoBSelect = document.getElementById('repo-b-select');
    const repoCSelect = document.getElementById('repo-c-select');
    
    const repoALabel = document.getElementById('repo-a-select-label');
    const repoBLabel = document.getElementById('repo-b-select-label');
    const repoCLabel = document.getElementById('repo-c-select-label');
    
    const repoBInfo = document.getElementById('repo-b-info');
    const repoCInfo = document.getElementById('repo-c-info');
    
    const submitBtn = document.getElementById('submit-btn');

    // 初始化
    function init() {
        updateRepoAOptions();
        updateDependencies();
        
        // 事件监听
        repoATypeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                updateRepoAOptions();
                updateDependencies();
            });
        });
        
        repoBTypeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                updateRepoBOptions();
                updateDependencies();
            });
        });
        
        repoCTypeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                updateRepoCOptions();
                updateDependencies();
            });
        });
        
        repoASelect.addEventListener('change', updateDependencies);
        repoBSelect.addEventListener('change', updateDependencies);
        repoCSelect.addEventListener('change', updateDependencies);
    }

    // 更新A仓库选项
    function updateRepoAOptions() {
        const type = document.querySelector('input[name="repo-a-type"]:checked').value;
        const options = type === 'branch' ? mockData.branches['repo-a'] : mockData.tags['repo-a'];
        
        repoALabel.textContent = type === 'branch' ? '选择分支' : '选择标签';
        repoASelect.innerHTML = '<option value="">请选择...</option>';
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            repoASelect.appendChild(optionElement);
        });
        
        repoASelect.value = '';
    }

    // 更新B仓库选项
    function updateRepoBOptions() {
        const type = document.querySelector('input[name="repo-b-type"]:checked').value;
        const options = type === 'branch' ? mockData.branches['repo-b'] : mockData.tags['repo-b'];
        
        repoBLabel.textContent = type === 'branch' ? '选择分支' : '选择标签';
        repoBSelect.innerHTML = '<option value="">请选择...</option>';
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            repoBSelect.appendChild(optionElement);
        });
        
        repoBSelect.value = '';
    }

    // 更新C仓库选项
    function updateRepoCOptions() {
        const type = document.querySelector('input[name="repo-c-type"]:checked').value;
        const options = type === 'branch' ? mockData.branches['repo-c'] : mockData.tags['repo-c'];
        
        repoCLabel.textContent = type === 'branch' ? '选择分支' : '选择标签';
        repoCSelect.innerHTML = '<option value="">请选择...</option>';
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            repoCSelect.appendChild(optionElement);
        });
        
        repoCSelect.value = '';
    }

    // 更新依赖关系
    function updateDependencies() {
        const repoAType = document.querySelector('input[name="repo-a-type"]:checked').value;
        const repoAValue = repoASelect.value;
        const repoBType = document.querySelector('input[name="repo-b-type"]:checked').value;
        const repoBValue = repoBSelect.value;
        
        // 更新B仓库状态和信息
        if (repoAType === 'branch' && repoAValue) {
            // A选择分支训练，B必须选择相同分支
            document.getElementById('repo-b-branch').checked = true;
            document.getElementById('repo-b-tag').disabled = true;
            updateRepoBOptions();
            
            // 限制B仓库只能选择相同分支名
            const matchingBranch = mockData.branches['repo-b'].find(branch => branch === repoAValue);
            if (matchingBranch) {
                repoBSelect.value = matchingBranch;
                repoBSelect.disabled = true;
                repoBInfo.innerHTML = `<strong>自动选择：</strong>必须使用与A仓库相同的分支 "${repoAValue}"`;
                repoBInfo.className = 'dependency-info warning';
            } else {
                repoBSelect.disabled = false;
                repoBInfo.innerHTML = `<strong>注意：</strong>A仓库选择分支训练，B仓库也必须选择分支训练`;
                repoBInfo.className = 'dependency-info';
            }
        } else if (repoAType === 'tag' && repoAValue) {
            // A选择标签，B可以选择标签或分支
            document.getElementById('repo-b-tag').disabled = false;
            repoBSelect.disabled = false;
            updateRepoBOptions();
            repoBInfo.innerHTML = `<strong>可选：</strong>A仓库选择标签，B仓库可以选择标签或分支训练`;
            repoBInfo.className = 'dependency-info';
        } else {
            // A未选择，B等待
            document.getElementById('repo-b-tag').disabled = false;
            repoBSelect.disabled = true;
            repoBInfo.innerHTML = '等待A仓库选择...';
            repoBInfo.className = 'dependency-info';
        }
        
        // 更新C仓库状态和信息
        if (repoBValue) {
            // B已选择，C必须跟随B的选择
            if (repoBType === 'branch') {
                document.getElementById('repo-c-branch').checked = true;
                document.getElementById('repo-c-tag').disabled = true;
                updateRepoCOptions();
                
                const matchingBranch = mockData.branches['repo-c'].find(branch => branch === repoBValue);
                if (matchingBranch) {
                    repoCSelect.value = matchingBranch;
                    repoCSelect.disabled = true;
                    repoCInfo.innerHTML = `<strong>自动选择：</strong>必须使用与B仓库相同的分支 "${repoBValue}"`;
                    repoCInfo.className = 'dependency-info warning';
                } else {
                    repoCSelect.disabled = false;
                    repoCInfo.innerHTML = `<strong>注意：</strong>B仓库选择分支训练，C仓库也必须选择分支训练`;
                    repoCInfo.className = 'dependency-info';
                }
            } else {
                document.getElementById('repo-c-tag').checked = true;
                document.getElementById('repo-c-branch').disabled = true;
                updateRepoCOptions();
                
                const matchingTag = mockData.tags['repo-c'].find(tag => tag === repoBValue);
                if (matchingTag) {
                    repoCSelect.value = matchingTag;
                    repoCSelect.disabled = true;
                    repoCInfo.innerHTML = `<strong>自动选择：</strong>必须使用与B仓库相同的标签 "${repoBValue}"`;
                    repoCInfo.className = 'dependency-info warning';
                } else {
                    repoCSelect.disabled = false;
                    repoCInfo.innerHTML = `<strong>注意：</strong>B仓库选择标签，C仓库也必须选择标签`;
                    repoCInfo.className = 'dependency-info';
                }
            }
        } else {
            // B未选择，C等待
            document.getElementById('repo-c-tag').disabled = false;
            document.getElementById('repo-c-branch').disabled = false;
            repoCSelect.disabled = true;
            repoCInfo.innerHTML = '等待B仓库选择...';
            repoCInfo.className = 'dependency-info';
        }
        
        // 更新卡片状态
        repoACard.classList.toggle('active', !!repoAValue);
        repoBCard.classList.toggle('active', !!repoBValue);
        repoCCard.classList.toggle('active', !!repoCSelect.value);
        
        repoBCard.classList.toggle('disabled', !repoAValue);
        repoCCard.classList.toggle('disabled', !repoBValue);
        
        // 更新提交按钮状态
        const allSelected = repoAValue && repoBValue && repoCSelect.value;
        submitBtn.disabled = !allSelected;
    }

    // 表单提交
    document.getElementById('training-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            repoA: {
                type: document.querySelector('input[name="repo-a-type"]:checked').value,
                value: repoASelect.value
            },
            repoB: {
                type: document.querySelector('input[name="repo-b-type"]:checked').value,
                value: repoBSelect.value
            },
            repoC: {
                type: document.querySelector('input[name="repo-c-type"]:checked').value,
                value: repoCSelect.value
            }
        };
        
        console.log('提交的训练任务配置:', formData);
        
        // 这里应该发送到后端API
        fetch('/create_training_task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('训练任务创建成功！');
                // 可以重定向到任务列表页面
            } else {
                alert('创建失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('创建失败，请重试');
        });
    });

    // 初始化页面
    init();
</script>
{% endblock %}
