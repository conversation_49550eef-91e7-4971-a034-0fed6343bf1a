{% extends "base.html" %}

{% block title %}步骤选择{% endblock %}

{% block content %}
<div class="steps-container">
    <h2>请选择处理步骤</h2>
    <form method="POST" action="{{ url_for('steps') }}">
        <div class="step-group">
            <h3>步骤一：选择处理类型</h3>
            <div class="radio-group">
                <label>
                    <input type="radio" name="step1" value="option1" required>
                    类型A处理
                </label>
                <label>
                    <input type="radio" name="step1" value="option2">
                    类型B处理
                </label>
            </div>
        </div>

        <div class="step-group">
            <h3>步骤二：选择处理方式</h3>
            <div class="radio-group">
                <label>
                    <input type="radio" name="step2" value="optionA" required>
                    方式A
                </label>
                <label>
                    <input type="radio" name="step2" value="optionB">
                    方式B
                </label>
            </div>
        </div>

        <button type="submit" class="submit-btn">确认并跳转</button>
    </form>
</div>

<style>
    .steps-container {
        max-width: 600px;
        background: var(--card-background);
        border-radius: 12px;
        box-shadow: 0 4px 16px var(--shadow-light);
        padding: 30px;
        margin: 20px auto;
    }
    
    .step-group {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid var(--border-color);
    }
    
    .step-group:last-child {
        border-bottom: none;
        margin-bottom: 10px;
    }
    
    h3 {
        color: var(--primary-color);
        margin-top: 0;
        margin-bottom: 15px;
    }
    
    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        padding: 10px;
        border-radius: 8px;
        transition: background-color 0.2s;
    }
    
    label:hover {
        background-color: rgba(26, 115, 232, 0.05);
    }
    
    input[type="radio"] {
        width: 18px;
        height: 18px;
    }
    
    .submit-btn {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.3s, transform 0.2s;
        display: block;
        margin: 20px auto 0;
        width: 100%;
        max-width: 200px;
    }
    
    .submit-btn:hover {
        background: var(--primary-dark-color);
        transform: translateY(-2px);
    }
</style>
{% endblock %}