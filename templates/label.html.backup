<!DOCTYPE html>
<html>

<head>
    <title>打标平台</title>
    <style>
        :root {
            --primary-color: #34495e;
            /* Dark Slate Blue */
            --primary-dark-color: #2c3e50;
            --secondary-color: #1abc9c;
            /* Turquoise */
            --background-color: #f8f9fa;
            /* Very Light Gray */
            --card-background: #ffffff;
            --text-color: #495057;
            /* Medium Dark Gray */
            --border-color: #ced4da;
            /* Light Grayish Blue */
            --shadow-light: rgba(0, 0, 0, 0.05);
            --shadow-medium: rgba(0, 0, 0, 0.1);
            --success-color: #28a745;
            /* Green for success */
            --danger-color: #dc3545;
            /* Red for danger */
        }

        /* General Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            min-height: 100vh;
        }

        /* 导航栏样式 */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
            padding: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            padding: 0 20px;
            height: 60px;
        }

        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            text-decoration: none;
            margin-right: 40px;
            letter-spacing: 0.5px;
        }

        .navbar-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            align-items: center;
        }

        .nav-item {
            margin: 0 8px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background-color: white;
            border-radius: 1px;
        }

        /* 主内容区域 */
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 40px;
            /* Increased margin */
            font-size: 2.8em;
            /* Slightly larger font */
            font-weight: 700;
            /* Bolder font */
            letter-spacing: 1.5px;
            text-shadow: 1px 1px 2px var(--shadow-light);
        }

        /* Card Container Styles */
        #content-lines,
        #export-form {
            width: 100%;
            max-width: 1200px;
            /* 增加最大宽度 */
            background-color: var(--card-background);
            border-radius: 15px;
            box-shadow: 0 15px 40px var(--shadow-medium);
            padding: 30px;
            margin-bottom: 30px;
            box-sizing: border-box;
        }

        /* Line Item Styles */
        .line-item {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 12px;
            padding: 12px 16px;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            /* 改回列布局 */
            gap: 12px;
            transition: all 0.2s ease-in-out;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .item-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            width: 100%;
        }

        .line-content {
            flex: 2;
            white-space: pre-wrap;
            word-break: break-all;
            background-color: #f3f4f6;
            /* 改为浅色背景 */
            color: #1f2937;
            /* 改为深色文字 */
            padding: 6px 12px;
            border-radius: 4px;
            font-family: 'Fira Code', 'Courier New', Courier, monospace;
            font-size: 0.92em;
            height: 32px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            /* 添加浅色边框 */
            line-height: 1.3;
        }

        .controls-and-tags {
            display: flex;
            flex-direction: column;
            gap: 12px;
            flex: 1;
        }

        .label-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            height: 32px;
        }

        .label-controls select {
            width: 140px;
            padding: 0 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.9em;
            background-color: white;
            cursor: pointer;
            height: 32px;
            color: var(--text-color);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none' stroke='%23495057' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 4l4 4 4-4'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 12px;
            padding-right: 28px;
            transition: all 0.2s ease;
        }

        .label-controls select:hover {
            border-color: #9ca3af;
        }

        .label-controls select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .confirm-btn {
            padding: 0 16px;
            height: 32px;
            font-size: 0.9em;
            background-color: #10b981;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            white-space: nowrap;
            font-weight: 500;
            transition: all 0.2s ease;
            min-width: 64px;
            justify-content: center;
        }

        .confirm-btn:hover {
            background-color: #059669;
            transform: translateY(-1px);
        }

        .confirm-btn:active {
            transform: translateY(0);
        }

        /* 标签样式优化 */
        .active-tags {
            display: none;
            /* 默认隐藏 */
            flex-wrap: wrap;
            gap: 8px;
            padding: 12px;
            background-color: #f8f9fa;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-top: -4px;
            /* 当显示时稍微往上移动一点 */
        }

        .active-tags.has-tags {
            display: flex;
            /* 有标签时才显示 */
        }

        .tag {
            background-color: #475569;
            color: white;
            padding: 4px 8px 4px 10px;
            border-radius: 4px;
            font-size: 0.85em;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            height: 24px;
            margin: 0;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .tag:hover {
            background-color: #3f4d5f;
        }

        .tag .remove-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            padding: 0;
            width: 16px;
            height: 16px;
            font-size: 14px;
            line-height: 1;
            cursor: pointer;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .tag .remove-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
        }

        /* 添加滚动条样式 */
        .line-content::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .line-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        .line-content::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        .line-content::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        /* 数据信息样式优化 */
        #data-info {
            background: #34495e;
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
            text-align: center;
            max-width: 1200px;
            width: 100%;
            box-sizing: border-box;
        }

        #data-info h2 {
            margin: 0;
            font-size: 1.1em;
            font-weight: 500;
        }

        #data-info p {
            margin: 6px 0 0 0;
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* Label Group Styles */
        .labels-wrapper {
            display: flex;
            flex-direction: column;
            gap: 15px;
            /* Increased gap */
            margin-top: 15px;
        }

        .label-group {
            display: flex;
            flex-wrap: wrap;
            /* Allow wrapping on smaller screens */
            align-items: center;
            gap: 15px;
            /* Increased gap */
            padding: 10px 15px;
            /* Increased padding */
            background-color: #f1f3f5;
            /* Lighter background */
            border-radius: 8px;
            /* More rounded corners */
            border: 1px solid #e0e0e0;
            /* Softer border */
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
            /* Inner shadow for depth */
        }

        .label-group label {
            font-weight: 600;
            /* Bolder label text */
            color: var(--primary-color);
            /* Primary color for labels */
            margin-right: 5px;
            min-width: 40px;
            /* Ensure labels have minimum width */
        }

        .label-group select {
            flex-grow: 1;
            /* Allow select to grow */
            padding: 10px 12px;
            /* Increased padding */
            border: 1px solid var(--border-color);
            border-radius: 8px;
            /* More rounded corners */
            font-size: 1em;
            /* Standard font size */
            background-color: #fff;
            color: var(--text-color);
            appearance: none;
            /* Remove default arrow */
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23495057%22%20d%3D%22M287%2C197.393L154.794%2C65.187c-4.756-4.756-12.476-4.756-17.232%2C0L5.393%2C197.393c-4.756%2C4.756-4.756%2C12.476%2C0%2C17.232l16.129%2C16.129c4.756%2C4.756%2C12.476%2C4.756%2C17.232%2C0l118.08-118.08l118.08%2C118.08c4.756%2C4.756%2C12.476%2C4.756%2C17.232%2C0l16.129-16.129C291.756%2C209.869%2C291.756%2C202.149%2C287%2C197.393z%22%2F%3E%3C%2Fsvg%3E');
            /* Custom arrow */
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 12px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .label-group select:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(26, 188, 156, 0.25);
        }

        /* Add Label Button */
        .add-label-group-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 38px;
            /* Larger button */
            height: 38px;
            /* Larger button */
            font-size: 1.8em;
            /* Larger plus sign */
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            align-self: flex-end;
            margin-top: 15px;
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 10px var(--shadow-light);
            font-weight: 300;
            /* Lighter font weight for plus */
        }

        .add-label-group-btn:hover {
            background-color: #16a085;
            /* Darker turquoise */
            transform: translateY(-3px) scale(1.05);
            /* More pronounced lift and slight scale */
            box-shadow: 0 6px 15px var(--shadow-medium);
        }

        .add-label-group-btn:active {
            transform: translateY(0) scale(1.0);
            box-shadow: 0 2px 5px var(--shadow-light);
        }

        /* Export Form Styles */
        #export-form {
            background: #fff;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 24px;
            margin-top: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        #export-form .form-group {
            margin-bottom: 24px;
        }

        #export-form label {
            display: block;
            font-size: 0.95em;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        #export-form input[type="text"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.95em;
            color: #374151;
            transition: all 0.2s ease;
        }

        #export-form input[type="text"]:hover {
            border-color: #9ca3af;
        }

        #export-form input[type="text"]:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        #export-form button {
            background-color: #1f2937;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 0.95em;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        #export-form button:hover {
            background-color: #111827;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        #export-form button:active {
            transform: translateY(0);
        }

        #export-form button::before {
            content: '';
            width: 20px;
            height: 20px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4'%3E%3C/path%3E%3C/svg%3E");
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
        }

        #false-positive-notice {
            max-width: 1200px !important;
            /* 增加最大宽度，与内容区域保持一致 */
        }

        /* 分隔线 */
        .tag-separator {
            width: 1px;
            height: 24px;
            background-color: #e5e7eb;
            margin: 0 4px;
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-container">
            <a href="/" class="navbar-brand">打标平台</a>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="/" class="nav-link">首页</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">

    <div id="data-info" style="
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
        max-width: 1200px;
        width: 100%;
        box-sizing: border-box;
    ">
        <h2 style="margin: 0; font-size: 1.5em;">数据信息</h2>
        <p style="margin: 10px 0 0 0;">
            总字符数：{{ total_chars }}，总行数：{{ total_lines }}
        </p>
    </div>

    {% if purpose == 'false_positive' %}
    <div id="false-positive-notice" style="
        background-color: var(--secondary-color);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
        max-width: 1200px;
        width: 100%;
        box-sizing: border-box;
    ">
        <h2 style="margin: 0; font-size: 1.5em;">误报标记模式</h2>
        <p style="margin: 10px 0 0 0;">在此模式下，所有标记的内容都将被标记为误报（benign = true）</p>
    </div>
    {% endif %}

    <div id="content-lines">
        {% if purpose == 'false_positive' %}
        <div class="empty-state" style="
                text-align: center;
                padding: 20px;
                background: var(--card-background);
                border-radius: 10px;
                box-shadow: 0 2px 4px var(--shadow-light);
                margin-bottom: 20px;
            ">
            <svg style="width: 48px; height: 48px; margin-bottom: 16px; color: var(--primary-color);"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 style="margin: 0 0 8px 0; color: var(--text-color);">误报样本标记模式</h3>
            <p style="margin: 0; color: var(--text-secondary);">在此模式下，所有标记的内容都将被标记为误报（benign = true）</p>
        </div>
        {% endif %}

        {% for line in processed_lines %}
        <div class="line-item" data-line-number="{{ line.line_number }}" data-payload="{{ line.payload }}">
            <div class="item-header">
                <div class="line-content">{{ line.display_content }}</div>
                {% if purpose != 'false_positive' %}
                <div class="label-controls">
                    <select class="risk-select">
                        <option value="">请选择风险等级</option>
                        {% for tag in risk_tags %}
                        <option value="{{ tag }}">{{ tag }}</option>
                        {% endfor %}
                    </select>
                    <select class="db-select">
                        <option value="">请选择数据库类型</option>
                        {% for tag in db_tags %}
                        <option value="{{ tag }}">{{ tag }}</option>
                        {% endfor %}
                    </select>
                    <button type="button" class="confirm-btn"
                        onclick="addTag(this.closest('.line-item'), this.parentElement.querySelector('.risk-select').value, this.parentElement.querySelector('.db-select').value)">确认</button>
                </div>
                {% endif %}
            </div>
            <div class="active-tags"></div>
        </div>
        {% endfor %}
    </div>

    <form id="export-form" action="/submit_labels" method="POST">
        <input type="hidden" name="purpose" id="purpose">
        <input type="hidden" name="export_filename" id="export_filename">
        <input type="hidden" name="data" id="data">
        <div class="form-group">
            <label for="filename">导出文件名:</label>
            <input type="text" id="filename" name="filename" value="labeled_data.json">
        </div>
        <button type="submit">导出标记数据</button>
    </form>

    <script>
        function createLabelGroup(lineItem) {
            const labelRow = document.createElement('div');
            labelRow.className = 'label-row';

            const riskSelect = document.createElement('select');
            riskSelect.className = 'risk-select';
            riskSelect.innerHTML = `
                <option value="">请选择风险等级</option>
                {% for tag in risk_tags %}
                <option value="{{ tag }}">{{ tag }}</option>
                {% endfor %}
            `;

            const dbSelect = document.createElement('select');
            dbSelect.className = 'db-select';
            dbSelect.innerHTML = `
                <option value="">请选择数据库类型</option>
                {% for tag in db_tags %}
                <option value="{{ tag }}">{{ tag }}</option>
                {% endfor %}
            `;

            const confirmBtn = document.createElement('button');
            confirmBtn.type = 'button';
            confirmBtn.className = 'confirm-btn';
            confirmBtn.innerHTML = `确认`;

            confirmBtn.onclick = () => {
                const risk = riskSelect.value;
                const db = dbSelect.value;

                if (!risk || !db) {
                    alert('请选择风险等级和数据库类型');
                    return;
                }

                addTag(lineItem, risk, db);
            };

            labelRow.appendChild(riskSelect);
            labelRow.appendChild(dbSelect);
            labelRow.appendChild(confirmBtn);

            return labelRow;
        }

        function addTag(lineItem, risk, db) {
            if (!risk || !db) {
                alert('请选择风险等级和数据库类型');
                return;
            }

            const activeTags = lineItem.querySelector('.active-tags');
            const tag = document.createElement('div');
            tag.className = 'tag';
            tag.innerHTML = `
                <span>风险: ${risk} | DB: ${db}</span>
                <button type="button" class="remove-btn" onclick="removeTag(this)">×</button>
            `;
            activeTags.appendChild(tag);
            activeTags.classList.add('has-tags');

            // 重置选择框
            const riskSelect = lineItem.querySelector('.risk-select');
            const dbSelect = lineItem.querySelector('.db-select');
            riskSelect.value = '';
            dbSelect.value = '';
        }

        function removeTag(button) {
            const tag = button.closest('.tag');
            const activeTags = tag.parentElement;
            tag.remove();

            // 如果没有标签了，移除has-tags类
            if (activeTags.children.length === 0) {
                activeTags.classList.remove('has-tags');
            }
        }

        document.getElementById('export-form').addEventListener('submit', async function (event) {
            // event.preventDefault();

            const filename = document.getElementById('filename').value;
            const urlParams = new URLSearchParams(window.location.search);
            const tmpfile_path = urlParams.get('tmpfile_path');
            const purpose = urlParams.get('purpose');

            if (!tmpfile_path) {
                alert('临时文件路径丢失！');
                return;
            }

            if (!purpose) {
                alert('未指定标记目的！');
                return;
            }

            // 收集标记数据
            let labeled_data = [];

            const lineItems = document.querySelectorAll('.line-item');

            if (purpose === 'false_positive') {
                // 误报模式：收集所有payload
                lineItems.forEach(item => {
                    labeled_data.push({
                        payload: item.dataset.payload
                    });
                });
            } else {
                // 黑样本模式：收集带标签的恶意样本
                lineItems.forEach(item => {
                    const tags = item.querySelector('.active-tags');
                    if (tags.children.length > 0) {
                        const tagElements = tags.querySelectorAll('.tag');
                        const sampleTags = Array.from(tagElements).map(tag => {
                            const [risk, db] = tag.querySelector('span').textContent
                                .split('|')
                                .map(part => part.split(':')[1].trim());
                            return { risk, db };
                        });

                        labeled_data.push({
                            payload: item.dataset.payload,
                            tags: sampleTags
                        });
                    } else {
                        labeled_data.push({
                            payload: item.dataset.payload,
                            tags: [],
                        });
                    }
                });
            }

            document.getElementById('purpose').value = purpose;
            document.getElementById('export_filename').value = filename;
            document.getElementById('data').value = JSON.stringify(labeled_data);

        });
    </script>
    </div> <!-- 关闭 main-content -->
</body>

</html>