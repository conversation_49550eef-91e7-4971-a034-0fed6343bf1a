{% extends "base.html" %}

{% block nav_home_active %}active{% endblock %}

{% block extra_css %}

        form {
            width: 100%;
            max-width: 1200px;  /* 增加最大宽度以适应并排布局 */
            padding: 32px;
            background-color: var(--card-background);
            border-radius: 16px;
            box-shadow: 0 1px 3px var(--shadow-light), 0 4px 8px var(--shadow-light);
            display: flex;
            flex-direction: column;
            gap: 24px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;
        }

        form:hover {
            box-shadow: 0 4px 8px var(--shadow-light), 0 8px 16px var(--shadow-light);
        }

        .input-sections-row {
            display: flex;
            gap: 24px;
            width: 100%;
            margin-bottom: 8px;
            box-sizing: border-box;
        }

        .input-section {
            flex: 1;
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            background-color: var(--card-background);
            box-shadow: 0 1px 3px var(--shadow-light);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 0;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .input-section h2 {
            margin: 0 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--primary-color);
            font-size: 1.25rem;
        }

        .form-group {
            margin: 0;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
            font-weight: 500;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        input[type="file"] {
            width: 100%;
            padding: 16px;
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            box-sizing: border-box;
            font-size: 1rem;
            color: var(--text-color);
            background-color: #fdfdfd;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        input[type="file"]:hover {
            border-color: var(--primary-color);
            background-color: rgba(26, 115, 232, 0.04);
        }

        input[type="file"]:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.1);
        }

        textarea {
            width: 100%;
            flex: 1;
            min-height: 200px;
            padding: 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-sizing: border-box;
            font-size: 1rem;
            color: var(--text-color);
            background-color: #fdfdfd;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            resize: none;
            font-family: inherit;
            margin-top: 8px;
        }

        textarea:hover {
            border-color: var(--text-secondary);
        }

        textarea:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.1);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .submit-button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
            color: white;
            padding: 0 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 700;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 6px 20px rgba(26, 115, 232, 0.3),
                0 2px 8px rgba(26, 115, 232, 0.2);
            position: relative;
            overflow: hidden;
            min-width: 100px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease-out, height 0.6s ease-out;
        }

        button:hover {
            background-color: var(--primary-dark-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-medium);
        }

        button:hover::after {
            width: 300px;
            height: 300px;
        }

        button:active {
            transform: translateY(0);
            box-shadow: 0 1px 3px var(--shadow-light);
        }

        .submit-button:hover {
            background: linear-gradient(135deg, var(--primary-dark-color), #0f4c75);
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 8px 25px rgba(26, 115, 232, 0.4),
                0 4px 12px rgba(26, 115, 232, 0.3);
        }

        .submit-button:active {
            transform: translateY(-1px) scale(0.98);
            box-shadow:
                0 4px 15px rgba(26, 115, 232, 0.3),
                0 2px 6px rgba(26, 115, 232, 0.2);
        }

        .submit-button:focus {
            outline: none;
            box-shadow:
                0 6px 20px rgba(26, 115, 232, 0.3),
                0 2px 8px rgba(26, 115, 232, 0.2),
                0 0 0 4px rgba(26, 115, 232, 0.2);
        }

        .submit-section {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 24px;
            padding: 16px 32px;
            border-radius: 16px;
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            margin-top: 8px;
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.08),
                0 2px 4px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }

        .purpose-selection {
            display: flex;
            align-items: center;
        }

        .radio-group {
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #ffffff, #f7fafc);
            padding: 6px;
            border-radius: 16px;
            margin: 0;
            position: relative;
            box-shadow:
                inset 0 2px 6px rgba(0, 0, 0, 0.06),
                0 6px 16px rgba(0, 0, 0, 0.1),
                0 2px 4px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.8);
            min-width: 320px;
            width: fit-content;
            backdrop-filter: blur(10px);
            height: 44px;
        }

        .radio-group input[type="radio"] {
            display: none;
        }

        .radio-group label {
            flex: 1;
            text-align: center;
            z-index: 2;
            padding: 8px 16px;
            font-size: 0.9rem;
            font-weight: 700;
            color: #4a5568;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 12px;
            user-select: none;
            margin: 0;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            height: 32px;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }

        .radio-group label::before {
            content: '';
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid currentColor;
            transition: all 0.3s ease;
            opacity: 0.6;
        }

        .radio-group label:hover {
            color: #2d3748;
            transform: translateY(-1px) scale(1.02);
        }

        .radio-group input[type="radio"]:checked + label {
            color: white;
            font-weight: 800;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transform: scale(1.02);
        }

        .radio-group input[type="radio"]:checked + label::before {
            background-color: currentColor;
            border-color: currentColor;
            opacity: 1;
            box-shadow:
                0 0 0 3px rgba(255, 255, 255, 0.4),
                0 2px 8px rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        /* 滑动背景 - 默认为红色（黑样本） */
        .radio-group::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            height: calc(100% - 12px);
            width: calc(50% - 6px);
            background: linear-gradient(135deg, #fc8181, #e53e3e, #c53030);
            border-radius: 12px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 6px 20px rgba(229, 62, 62, 0.4),
                0 2px 8px rgba(229, 62, 62, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 当选择误报样本时，滑块移动到右边并变成蓝色 */
        .radio-group.fp-selected::after {
            transform: translateX(calc(100% + 6px));
            background: linear-gradient(135deg, #63b3ed, #3182ce, #2c5282);
            box-shadow:
                0 6px 20px rgba(49, 130, 206, 0.4),
                0 2px 8px rgba(49, 130, 206, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* 移除表情图标 */

        /* 增强悬停效果 */
        .radio-group label:hover::before {
            transform: scale(1.2);
        }

        /* 添加点击动画 */
        .radio-group label:active {
            transform: translateY(0) scale(0.98);
        }

        /* 增加焦点样式 */
        .radio-group input[type="radio"]:focus + label {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* 添加微妙的发光效果 */
        .radio-group:hover {
            box-shadow:
                inset 0 2px 6px rgba(0, 0, 0, 0.06),
                0 6px 20px rgba(0, 0, 0, 0.12),
                0 4px 8px rgba(0, 0, 0, 0.06);
        }

        /* 选中状态的额外发光 */
        .radio-group::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            border-radius: 22px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 0;
        }

        .radio-group:hover::before {
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .radio-group {
                min-width: 280px;
                flex-direction: column;
                height: auto;
                padding: 8px;
            }

            .radio-group::after {
                width: calc(100% - 16px);
                height: calc(50% - 8px);
                top: 8px;
                left: 8px;
                transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .radio-group.fp-selected::after {
                transform: translateY(calc(100% + 8px));
            }

            .radio-group label {
                height: 36px;
                padding: 8px 20px;
                font-size: 0.9rem;
            }
        }

        .file-upload-zone {
            flex: 1;
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            min-height: 200px;
            transition: all 0.3s ease;
            background-color: #fdfdfd;
        }

        .file-upload-zone.has-file {
            border-color: var(--primary-color);
            background-color: rgba(26, 115, 232, 0.04);
        }

        .file-upload-zone .upload-icon {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .file-upload-zone .upload-text {
            text-align: center;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .file-upload-zone .selected-files {
            display: none;
            flex-direction: column;
            gap: 8px;
            margin-top: 16px;
            width: 100%;
            position: relative;
            z-index: 5;
            pointer-events: auto;
        }

        .file-upload-zone.has-file .selected-files {
            display: flex;
        }

        .selected-file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px;
            font-size: 0.9em;
        }

        .selected-file-item .file-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .selected-file-item .remove-file {
            background: none;
            border: none;
            color: white;
            margin-left: 8px;
            cursor: pointer;
            padding: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 14px;
            transition: background-color 0.3s ease;
            position: relative;
            z-index: 10;
            pointer-events: auto;
        }

        .selected-file-item .remove-file:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .files-summary {
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.9em;
            margin-top: 8px;
        }

        .reselect-files {
            text-align: center;
            margin-top: 12px;
        }

        .reselect-files button {
            background: none;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .reselect-files button:hover {
            background-color: var(--primary-color);
            color: white;
        }



        .file-upload-zone input[type="file"] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            pointer-events: none;
        }

        .file-upload-zone:not(.has-file) input[type="file"] {
            pointer-events: auto;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .file-upload-zone.dragover {
            border-color: var(--primary-color);
            background-color: rgba(26, 115, 232, 0.08);
        }

        .file-upload-zone .upload-icon {
            animation: bounce 2s infinite;
        }

        @media (max-width: 768px) {
            .navbar-container {
                padding: 0 16px;
            }

            .navbar-brand {
                font-size: 1.3rem;
                margin-right: 20px;
            }

            .main-content {
                padding: 20px 16px;
            }

            form {
                padding: 20px;
            }

            h1 {
                font-size: 2rem;
                margin-bottom: 32px;
            }

            .input-sections-row {
                flex-direction: column;
                gap: 20px;
            }

            .input-section {
                padding: 20px;
            }

            .submit-section {
                flex-direction: column;
                align-items: stretch;
                padding: 20px;
                gap: 20px;
            }

            .purpose-selection {
                align-items: center;
            }

            .radio-group {
                min-width: 280px;
            }

            .submit-button {
                width: 100%;
                max-width: 280px;
                align-self: center;
                padding: 14px 32px;
                font-size: 1rem;
                height: auto;
            }
        }

        /* 添加加载动画 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .loading {
            animation: pulse 1.5s infinite ease-in-out;
            opacity: 0.7;
            pointer-events: none;
        }

        /* 添加成功提示动画 */
        @keyframes slideIn {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--success-color);
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease-out;
            z-index: 1000;
        }

        /* 优化格式切换开关样式 */
        .format-switch {
            display: flex;
            align-items: center;
            background: var(--background-color);
            padding: 4px;
            border-radius: 30px;
            margin-bottom: 16px;
            width: fit-content;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
            border: 1px solid var(--border-color);
            min-width: 200px;
        }

        .format-switch input[type="checkbox"] {
            display: none;
        }

        .format-switch label {
            flex: 1;
            text-align: center;
            z-index: 1;
            padding: 8px 16px;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 20px;
            user-select: none;
            margin: 0;
            text-transform: none;
        }

        .format-switch .slider {
            position: absolute;
            top: 4px;
            left: 4px;
            height: calc(100% - 8px);
            width: calc(50% - 4px);
            background-color: white;
            border-radius: 20px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            pointer-events: none;
        }

        .format-switch input[type="checkbox"]:checked ~ .slider {
            transform: translateX(calc(100% - 0px));
        }

        .format-switch input[type="checkbox"]:checked ~ label[for="format-toggle-reprb"] {
            color: var(--primary-color);
        }

        .format-switch input[type="checkbox"]:not(:checked) ~ label[for="format-toggle-utf8"] {
            color: var(--primary-color);
        }

        .format-switch label:hover {
            opacity: 0.9;
        }

        /* 移除旧的开关样式 */
        .switch, .switch input, .switch .slider {
            display: none;
        }

        /* 禁用提示样式 */
        .disabled-notice {
            background-color: #f0f4ff;
            border: 1px solid #d1e7ff;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            text-align: center;
            color: var(--text-secondary);
        }

        .disabled-notice p {
            margin: 4px 0;
        }

        /* 禁用状态的文本框样式 */
        textarea:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 禁用状态的格式切换 */
        .format-switch.disabled {
            opacity: 0.5;
            pointer-events: none;
        }
{% endblock %}

{% block content %}
    <form id="labeling-form" enctype="multipart/form-data">
        <div class="input-sections-row">
            <div class="input-section">
                <h2>单个文本打标</h2>
                <div class="form-group">
                    <div class="format-switch">
                        <input type="checkbox" id="format-toggle" name="format" value="reprb">
                        <label for="format-toggle-utf8" id="utf8-label">UTF-8</label>
                        <label for="format-toggle-reprb" id="reprb-label">REPRB</label>
                        <span class="slider"></span>
                    </div>
                    <label for="manual_text">手动输入文本:</label>
                    <div id="text-disabled-notice" class="disabled-notice" style="display: none;">
                        <p>已选择文件上传，文本输入已禁用</p>
                        <p style="font-size: 0.9em; opacity: 0.8;">如需使用文本输入，请刷新页面重新选择</p>
                    </div>
                    <textarea id="manual_text" name="manual_text" placeholder="在此输入要打标的文本..."></textarea>
                </div>
            </div>

            <div class="input-section">
                <h2>批量文件打标</h2>
                <div class="form-group">
                    <label for="file">选择文件</label>
                    <div class="file-upload-zone" id="fileUploadZone">
                        <div class="upload-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" fill="#FFD54F"/>
                                <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" fill="none" stroke="#F57F17" stroke-width="0.5"/>
                                <path d="M2 8h20v10c0 1.11-.89 2-2 2H4c-1.11 0-2-.89-2-2V8z" fill="#FFC107"/>
                                <path d="M2 8h20v2H2V8z" fill="#FF8F00"/>
                            </svg>
                        </div>
                        <div class="upload-text">
                            <p>拖拽文件到这里或点击选择文件</p>
                            <p style="font-size: 0.9em; color: var(--text-secondary);">支持 .reprb 文件</p>
                        </div>
                        <div class="selected-files" id="selectedFiles">
                            <!-- 选中的文件列表将在这里显示 -->
                        </div>
                        <input type="file" id="file" name="file" accept=".reprb" multiple>
                    </div>
                </div>
            </div>
        </div>

        <div class="submit-section">
            <div class="purpose-selection">
                <div class="radio-group">
                    <input type="radio" id="purpose_malicious" name="purpose" value="malicious" required checked>
                    <label for="purpose_malicious">添加黑样本</label>
                    <input type="radio" id="purpose_fp" name="purpose" value="false_positive" required>
                    <label for="purpose_fp">添加误报样本</label>
                </div>
            </div>
            <button type="submit" class="submit-button">提交</button>
        </div>
    </form>

    <script>
        // 更新格式切换逻辑
        const formatToggle = document.getElementById('format-toggle');
        const utf8Label = document.getElementById('utf8-label');
        const reprbLabel = document.getElementById('reprb-label');

        utf8Label.addEventListener('click', function() {
            formatToggle.checked = false;
        });

        reprbLabel.addEventListener('click', function() {
            formatToggle.checked = true;
        });

        // 处理目的选择的滑动效果
        const radioGroup = document.querySelector('.radio-group');
        const purposeRadios = document.querySelectorAll('input[name="purpose"]');

        function updateRadioGroupStyle() {
            const fpRadio = document.getElementById('purpose_fp');
            if (fpRadio.checked) {
                radioGroup.classList.add('fp-selected');
            } else {
                radioGroup.classList.remove('fp-selected');
            }
        }

        // 初始化样式
        updateRadioGroupStyle();

        // 监听变化
        purposeRadios.forEach(radio => {
            radio.addEventListener('change', updateRadioGroupStyle);
        });

        document.getElementById('labeling-form').addEventListener('submit', async function(event) {
            event.preventDefault();

            const form = this;
            const fileInput = document.getElementById('file');
            const manualTextInput = document.getElementById('manual_text');
            const submitButton = form.querySelector('button[type="submit"]');
            const purpose = document.querySelector('input[name="purpose"]:checked');
            const isReprb = formatToggle.checked;

            // 验证目的选择
            if (!purpose) {
                showMessage('请选择标记目的！', 'error');
                return;
            }

            let formData = new FormData();
            let endpoint = '';

            // 验证输入
            if (fileInput.files.length === 0 && manualTextInput.value.trim() === '') {
                showMessage('请上传文件或手动输入文本！', 'error');
                return;
            }

            if (fileInput.files.length > 0) {
                if (fileInput.files.length === 1) {
                    // 单文件上传
                    formData.append('file', fileInput.files[0]);
                    endpoint = '/upload_file';
                } else {
                    // 多文件上传
                    for (let i = 0; i < fileInput.files.length; i++) {
                        formData.append('files', fileInput.files[i]);
                    }
                    endpoint = '/upload_files';
                }
            } else {
                formData.append('text', manualTextInput.value.trim());
                formData.append('format', isReprb ? 'reprb' : 'utf8');
                endpoint = '/process_text';
            }

            // 添加purpose到formData
            formData.append('purpose', purpose.value);

            // 添加加载状态
            submitButton.classList.add('loading');
            submitButton.disabled = true;

            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    if (data.tmpfile_path) {
                        showMessage('提交成功！', 'success');
                        // 使用 tmpfile_path 和 purpose 参数进行重定向
                        window.location.href = `/label_page?tmpfile_path=${encodeURIComponent(data.tmpfile_path)}&purpose=${encodeURIComponent(purpose.value)}`;
                    } else {
                        showMessage('服务器响应错误：未返回文件路径', 'error');
                    }
                } else {
                    showMessage(data.message || '提交失败，请重试', 'error');
                }
            } catch (error) {
                showMessage('发生错误，请重试', 'error');
                console.error('Error:', error);
            } finally {
                submitButton.classList.remove('loading');
                submitButton.disabled = false;
            }
        });

        const fileUploadZone = document.getElementById('fileUploadZone');
        const fileInput = document.getElementById('file');
        const selectedFiles = document.getElementById('selectedFiles');
        const manualTextInput = document.getElementById('manual_text');

        // 存储选中的文件
        let selectedFilesList = [];

        // 处理拖拽事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileUploadZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            fileUploadZone.addEventListener(eventName, highlight, false);
        });

        fileUploadZone.addEventListener('dragleave', function(e) {
            // 只有当鼠标真正离开拖拽区域时才移除高亮
            if (!fileUploadZone.contains(e.relatedTarget)) {
                unhighlight();
            }
        }, false);

        fileUploadZone.addEventListener('drop', unhighlight, false);

        function highlight() {
            fileUploadZone.classList.add('dragover');
        }

        function unhighlight() {
            fileUploadZone.classList.remove('dragover');
        }

        // 处理文件选择
        fileInput.addEventListener('change', handleFileSelect);
        fileUploadZone.addEventListener('drop', handleDrop);

        // 处理文本输入时清除文件选择
        manualTextInput.addEventListener('input', function() {
            if (this.value.trim() !== '' && fileInput.files.length > 0) {
                clearFiles();
                showMessage('已清除文件选择，切换到文本输入模式', 'success');
            }
        });

        // 处理删除文件按钮和重新选择按钮的点击（事件委托）
        selectedFiles.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-file')) {
                const index = parseInt(e.target.getAttribute('data-index'));
                removeFile(index);
            } else if (e.target.id === 'reselectButton') {
                // 触发文件选择
                fileInput.click();
            }
        });

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                // 验证所有文件类型
                const invalidFiles = files.filter(file => !file.name.toLowerCase().endsWith('.reprb'));
                if (invalidFiles.length > 0) {
                    showMessage(`以下文件格式不正确：${invalidFiles.map(f => f.name).join(', ')}`, 'error');
                    clearFiles();
                    return;
                }
                updateFilesInfo(files);
            } else {
                // 如果没有选择文件，确保文本输入是启用的
                enableTextInput();
            }
        }

        function handleDrop(e) {
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                // 验证所有文件类型
                const invalidFiles = files.filter(file => !file.name.toLowerCase().endsWith('.reprb'));
                if (invalidFiles.length > 0) {
                    showMessage(`以下文件格式不正确：${invalidFiles.map(f => f.name).join(', ')}`, 'error');
                    return;
                }

                // 创建一个新的 DataTransfer 对象来设置文件
                const dt = new DataTransfer();
                files.forEach(file => dt.items.add(file));
                fileInput.files = dt.files;
                updateFilesInfo(files);
            }
        }

        function updateFilesInfo(files) {
            selectedFilesList = Array.from(files);
            renderSelectedFiles();
            fileUploadZone.classList.add('has-file');

            // 禁用文本输入区域
            disableTextInput();
        }

        function renderSelectedFiles() {
            selectedFiles.innerHTML = '';

            if (selectedFilesList.length === 0) {
                return;
            }

            selectedFilesList.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'selected-file-item';
                fileItem.innerHTML = `
                    <span class="file-name" title="${file.name}">${file.name}</span>
                    <button type="button" class="remove-file" data-index="${index}">✕</button>
                `;
                selectedFiles.appendChild(fileItem);
            });

            // 添加文件总结
            const summary = document.createElement('div');
            summary.className = 'files-summary';
            summary.textContent = `已选择 ${selectedFilesList.length} 个文件`;
            selectedFiles.appendChild(summary);

            // 添加重新选择按钮
            const reselectDiv = document.createElement('div');
            reselectDiv.className = 'reselect-files';
            reselectDiv.innerHTML = '<button type="button" id="reselectButton">重新选择文件</button>';
            selectedFiles.appendChild(reselectDiv);
        }

        function removeFile(index) {
            selectedFilesList.splice(index, 1);

            // 更新文件输入框
            const dt = new DataTransfer();
            selectedFilesList.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;

            if (selectedFilesList.length === 0) {
                clearFiles();
            } else {
                renderSelectedFiles();
            }
        }

        // 保留旧函数以兼容性（虽然不再使用）
        function updateFileInfo(file) {
            updateFilesInfo([file]);
        }

        function disableTextInput() {
            const manualTextInput = document.getElementById('manual_text');
            const formatSwitch = document.querySelector('.format-switch');
            const disabledNotice = document.getElementById('text-disabled-notice');

            // 禁用文本框
            manualTextInput.disabled = true;
            manualTextInput.placeholder = '已选择文件上传，文本输入已禁用';

            // 禁用格式切换
            formatSwitch.classList.add('disabled');

            // 显示禁用提示
            disabledNotice.style.display = 'block';
        }

        function enableTextInput() {
            const manualTextInput = document.getElementById('manual_text');
            const formatSwitch = document.querySelector('.format-switch');
            const disabledNotice = document.getElementById('text-disabled-notice');

            // 启用文本框
            manualTextInput.disabled = false;
            manualTextInput.placeholder = '在此输入要打标的文本...';

            // 启用格式切换
            formatSwitch.classList.remove('disabled');

            // 隐藏禁用提示
            disabledNotice.style.display = 'none';
        }

        function clearFiles() {
            fileInput.value = '';
            selectedFilesList = [];
            selectedFiles.innerHTML = '';
            fileUploadZone.classList.remove('has-file');

            // 重新启用文本输入区域
            enableTextInput();
        }

        // 保留旧函数以兼容性
        function clearFile() {
            clearFiles();
        }



        // 显示消息提示
        function showMessage(message, type = 'success') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type === 'success' ? 'success-message' : 'error-message'}`;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 添加文件拖放样式
        const style = document.createElement('style');
        style.textContent = `
            .highlight {
                border-color: var(--primary-color) !important;
                background-color: rgba(26, 115, 232, 0.08) !important;
            }

            .error-message {
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: var(--error-color);
                color: white;
                padding: 16px 24px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: slideIn 0.3s ease-out;
                z-index: 1000;
            }
        `;
        document.head.appendChild(style);
    </script>
{% endblock %}

{% block extra_js %}
{% endblock %}