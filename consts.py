from typing import Tuple, Dict
from itertools import product

DBFlags = Tuple[int, int]
ALL_DBFLAGS: Dict[str, DBFlags] = {
    "COMMON": (0x01, 0),
    "MYSQL": (0x02, 0),
    "MYSQL_WITH_MYSQL_COMMENT": (0x02, 0x01),
    "ORACLE": (0x04, 0),
    "SQLITE": (0x08, 0),
    "DB2": (0x10, 0),
    "MSSQL": (0x20, 0),
    "POSTGRES": (0x40, 0),
    "H2": (0x80, 0),
}

ALL_beginchar: Dict[str, int] = {
    "NULL": b"\0"[0],
    "SINGLE": b"'"[0],
    "DOUBLE": b'"'[0],
    "BACKTICK": b"`"[0],
}

ALL_RISK: Dict[str, int] = {
    "NORMAL": 0,
    "LOW": 1,
    "MEDIUM": 2,
    "HIGH": 3,
}

ALL_ASSUMPTIONS = [
    (a, b, c) for a, (b, c) in product(ALL_beginchar.values(), ALL_DBFLAGS.values())
]
